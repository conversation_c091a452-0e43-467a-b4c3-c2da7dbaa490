-- Database verification script
-- Run this to check for data integrity issues

-- Check for duplicate backgrounds (this might be causing the issue)
SELECT 
    c.novel_id,
    c.chapter_number,
    b.scene_number,
    COUNT(*) as duplicate_count
FROM backgrounds b
JOIN chapters c ON b.chapter_id = c.id
GROUP BY c.novel_id, c.chapter_number, b.scene_number
HAVING COUNT(*) > 1;

-- Check all backgrounds with their chapter info
SELECT 
    n.title as novel_title,
    c.chapter_number,
    c.title as chapter_title,
    b.scene_number,
    b.name as background_name,
    b.id as background_id
FROM novels n
JOIN chapters c ON n.id = c.novel_id
JOIN backgrounds b ON c.id = b.chapter_id
ORDER BY n.id, c.chapter_number, b.scene_number;

-- Check for orphaned backgrounds (backgrounds without valid chapters)
SELECT b.* 
FROM backgrounds b
LEFT JOIN chapters c ON b.chapter_id = c.id
WHERE c.id IS NULL;

-- Check for orphaned chapters (chapters without valid novels)
SELECT c.* 
FROM chapters c
LEFT JOIN novels n ON c.novel_id = n.id
WHERE n.id IS NULL;

-- Check migration history
SELECT * FROM flyway_schema_history ORDER BY installed_rank;

-- Check table constraints
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name IN ('backgrounds', 'chapters', 'dialogues', 'game_actions', 'game_sessions')
ORDER BY tc.table_name, tc.constraint_name;
