/* Custom CSS to complement Bootstrap for PPT Game */

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Game-specific styles that work with Bootstrap */
.game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

.game-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
}

/* Weather Effects */
.weather-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

.weather-overlay.storm::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.1) 2px,
        rgba(255,255,255,0.1) 4px
    );
    animation: rain 0.3s linear infinite;
}

@keyframes rain {
    0% { transform: translateX(-10px); }
    100% { transform: translateX(10px); }
}

/* Time of Day Effects */
.time-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

.time-overlay.morning {
    background: linear-gradient(to bottom, rgba(255, 223, 186, 0.1) 0%, transparent 50%);
}

.time-overlay.afternoon {
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.time-overlay.evening {
    background: linear-gradient(to bottom, rgba(255, 165, 0, 0.2) 0%, transparent 50%);
}

.time-overlay.night {
    background: linear-gradient(to bottom, rgba(0, 0, 50, 0.4) 0%, transparent 50%);
}

/* Character Display */
.character-area {
    position: absolute;
    bottom: 20%;
    left: 0;
    width: 100%;
    height: 60%;
    z-index: 3;
    pointer-events: none;
}

.character-display {
    position: absolute;
    bottom: 0;
    height: 100%;
    transition: all 0.5s ease;
}

.character-display.left {
    left: 10%;
}

.character-display.center {
    left: 50%;
    transform: translateX(-50%);
}

.character-display.right {
    right: 10%;
}

.character-image {
    max-height: 100%;
    max-width: 300px;
    object-fit: contain;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.5));
}

/* UI Overlay */
.ui-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    pointer-events: none;
}

.ui-overlay > * {
    pointer-events: auto;
}

/* Top Bar */
.top-bar {
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
    padding: 1rem 2rem;
}

/* Progress Bar */
.progress-bar-custom {
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    transition: width 0.3s ease;
}

/* Dialogue Container */
.dialogue-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 2rem;
}

.dialogue-box {
    background: rgba(0, 0, 0, 0.85);
    border-radius: 15px;
    padding: 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dialogue-box:hover {
    background: rgba(0, 0, 0, 0.9);
    border-color: rgba(255, 255, 255, 0.2);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.dialogue-box.narration {
    border-left: 4px solid #4ecdc4;
}

.dialogue-box.speech {
    border-left: 4px solid #ff6b6b;
}

.character-name {
    font-weight: bold;
    color: #4ecdc4;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.dialogue-text {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #fff;
}

/* Emotion Indicators */
.emotion-indicator {
    position: absolute;
    top: -10px;
    right: 20px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.emotion-indicator.excited {
    background: #ffeb3b;
    animation: pulse 1s infinite;
}

.emotion-indicator.sad {
    background: #2196f3;
}

.emotion-indicator.angry {
    background: #f44336;
}

.emotion-indicator.neutral {
    background: #9e9e9e;
}

.emotion-indicator.happy {
    background: #4caf50;
}

.emotion-indicator.surprised {
    background: #ff9800;
}

.emotion-indicator.worried {
    background: #9c27b0;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Control Panel - Right Corner */
.control-panel {
    position: absolute;
    bottom: 2rem;
    right: 2rem;
    display: flex;
    gap: 1rem;
    z-index: 20;
}

/* Menu Button - Left Corner */
.menu-button-container {
    position: absolute;
    bottom: 2rem;
    left: 2rem;
    z-index: 20;
}

/* Game Buttons */
.btn-game {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-game:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.1);
    border-color: #4ecdc4;
    color: white;
    transform: translateY(-2px);
}

.btn-game:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-next {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    border-color: #4ecdc4;
}

.btn-prev {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    border-color: #ff6b6b;
}

/* Menu Overlay */
.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: none;
}

.menu-content {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .top-bar {
        padding: 1rem;
    }

    .control-panel {
        flex-direction: column;
        bottom: 1rem;
        right: 1rem;
        gap: 0.5rem;
    }

    .menu-button-container {
        bottom: 1rem;
        left: 1rem;
    }

    .dialogue-container {
        padding: 1rem;
        padding-bottom: 8rem; /* Make room for buttons */
    }

    .character-image {
        max-width: 200px;
    }

    .dialogue-text {
        font-size: 1rem;
    }

    .btn-game {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Audio Controls (hidden but functional) */
#backgroundMusic,
#ambientSound,
#voiceAudio {
    display: none;
}
