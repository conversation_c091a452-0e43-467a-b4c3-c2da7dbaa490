// Game JavaScript functionality with jQuery and Bootstrap
class GameController {
    constructor() {
        this.sessionId = window.gameData.sessionId;
        this.currentDialogueIndex = window.gameData.currentDialogueIndex;
        this.totalDialogues = window.gameData.totalDialogues;
        this.hasNext = window.gameData.hasNext;
        this.hasPrevious = window.gameData.hasPrevious;
        this.currentBackground = window.gameData.currentBackground;
        this.dialogues = window.gameData.dialogues;
        this.gameActions = window.gameData.gameActions;
        
        this.initializeEventListeners();
        this.initializeAudio();
        this.initializeScene();
    }
    
    initializeEventListeners() {
        // Keyboard controls
        $(document).on('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                case 'Enter':
                    e.preventDefault();
                    this.nextDialogue();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.previousDialogue();
                    break;
                case 'Escape':
                    e.preventDefault();
                    this.toggleMenu();
                    break;
                case 's':
                case 'S':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.saveGame();
                    }
                    break;
            }
        });
    }
    
    initializeAudio() {
        this.backgroundMusic = document.getElementById('backgroundMusic');
        this.ambientSound = document.getElementById('ambientSound');
        this.voiceAudio = document.getElementById('voiceAudio');
        
        // Set volume levels
        if (this.backgroundMusic) this.backgroundMusic.volume = 0.3;
        if (this.ambientSound) this.ambientSound.volume = 0.2;
        if (this.voiceAudio) this.voiceAudio.volume = 0.8;
    }
    
    initializeScene() {
        // Execute scene start actions
        this.executeGameActions('SCENE_START');
        
        // Load background music and ambient sounds
        if (this.currentBackground && this.currentBackground.backgroundMusicUrl) {
            this.playBackgroundMusic(this.currentBackground.backgroundMusicUrl);
        }
        
        if (this.currentBackground && this.currentBackground.ambientSoundUrl) {
            this.playAmbientSound(this.currentBackground.ambientSoundUrl);
        }
        
        // Update character image based on current dialogue emotion
        this.updateCharacterImage();
    }
    
    executeGameActions(triggerCondition) {
        if (!this.gameActions) return;
        
        const actionsToExecute = this.gameActions.filter(action => 
            action.triggerCondition === triggerCondition
        ).sort((a, b) => a.sequenceOrder - b.sequenceOrder);
        
        actionsToExecute.forEach(action => {
            setTimeout(() => {
                this.executeAction(action);
            }, action.delayBeforeMs || 0);
        });
    }
    
    executeAction(action) {
        switch (action.actionType) {
            case 'PLAY_MUSIC':
                this.playBackgroundMusic(action.actionValue);
                break;
            case 'PLAY_AMBIENT':
                this.playAmbientSound(action.actionValue);
                break;
            case 'FADE_IN':
                this.fadeInScene(parseInt(action.actionValue));
                break;
            case 'WEATHER_EFFECT':
                this.applyWeatherEffect(action.actionValue);
                break;
        }
    }
    
    playBackgroundMusic(url) {
        if (this.backgroundMusic && url) {
            this.backgroundMusic.src = url;
            this.backgroundMusic.play().catch(e => console.log('Background music autoplay prevented:', e));
        }
    }
    
    playAmbientSound(url) {
        if (this.ambientSound && url) {
            this.ambientSound.src = url;
            this.ambientSound.play().catch(e => console.log('Ambient sound autoplay prevented:', e));
        }
    }
    
    fadeInScene(duration) {
        $('.game-container').css('opacity', '0').animate({opacity: 1}, duration);
    }
    
    applyWeatherEffect(effect) {
        $('.weather-overlay').removeClass().addClass('weather-overlay ' + effect.toLowerCase());
    }
    
    updateCharacterImage() {
        const currentDialogue = this.dialogues.find(d => d.sequenceOrder === this.currentDialogueIndex);
        if (!currentDialogue || !currentDialogue.character) return;
        
        const characterImg = $(`#character-${currentDialogue.character.id}`);
        if (characterImg.length === 0) return;
        
        // Parse emotional images JSON
        let emotionalImages = {};
        try {
            if (currentDialogue.character.emotionalImages) {
                emotionalImages = JSON.parse(currentDialogue.character.emotionalImages);
            }
        } catch (e) {
            console.log('Error parsing emotional images:', e);
        }
        
        // Get the appropriate image for the current emotion
        const emotion = currentDialogue.emotion || 'NEUTRAL';
        const emotionalImageUrl = emotionalImages[emotion] || currentDialogue.character.avatarUrl;
        
        if (emotionalImageUrl && characterImg.attr('src') !== emotionalImageUrl) {
            characterImg.attr('src', emotionalImageUrl);
        }
    }
    
    async makeApiCall(url, method = 'POST', data = null) {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(url, options);
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || 'API call failed');
            }
            
            return result.data;
        } catch (error) {
            console.error('API call failed:', error);
            this.showError('Network error. Please try again.');
            throw error;
        }
    }
    
    async nextDialogue() {
        if (!this.hasNext) return;
        
        try {
            const gameState = await this.makeApiCall(`/api/game/session/${this.sessionId}/next-dialogue`);
            this.updateGameState(gameState);
        } catch (error) {
            console.error('Failed to advance dialogue:', error);
        }
    }
    
    async previousDialogue() {
        if (!this.hasPrevious) return;
        
        try {
            const gameState = await this.makeApiCall(`/api/game/session/${this.sessionId}/previous-dialogue`);
            this.updateGameState(gameState);
        } catch (error) {
            console.error('Failed to go back dialogue:', error);
        }
    }
    
    async saveGame() {
        try {
            await this.makeApiCall(`/api/game/session/${this.sessionId}/save`);
            this.showMessage('Game saved successfully!', 'success');
        } catch (error) {
            console.error('Failed to save game:', error);
            this.showMessage('Failed to save game', 'danger');
        }
    }
    
    restartGame() {
        if (confirm('Are you sure you want to restart the game? All progress will be lost.')) {
            window.location.href = '/game/start';
        }
    }
    
    updateGameState(gameState) {
        // Update game data
        this.currentDialogueIndex = gameState.currentDialogueIndex;
        this.hasNext = gameState.hasNext;
        this.hasPrevious = gameState.hasPrevious;
        
        // Reload the page to update the UI with new game state
        window.location.reload();
    }
    
    showMenu() {
        $('#gameMenu').show();
    }
    
    hideMenu() {
        $('#gameMenu').hide();
    }
    
    toggleMenu() {
        $('#gameMenu').toggle();
    }
    
    returnToStart() {
        if (confirm('Are you sure you want to return to the start? Unsaved progress will be lost.')) {
            window.location.href = '/game/start';
        }
    }
    
    showMessage(message, type = 'info') {
        const alertClass = `alert-${type}`;
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 2000; min-width: 300px;" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('body').append(alertHtml);
        
        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            $('.alert').alert('close');
        }, 3000);
    }
    
    showError(message) {
        this.showMessage(`Error: ${message}`, 'danger');
    }
    
    endGame() {
        this.showMessage('Congratulations! You have completed the game!', 'success');
        setTimeout(() => {
            if (confirm('Game completed! Would you like to return to the start?')) {
                window.location.href = '/game/start';
            }
        }, 3000);
    }
}

// Global functions for template usage
function nextDialogue() {
    if (window.gameController) {
        window.gameController.nextDialogue();
    }
}

function previousDialogue() {
    if (window.gameController) {
        window.gameController.previousDialogue();
    }
}

function saveGame() {
    if (window.gameController) {
        window.gameController.saveGame();
    }
}

function restartGame() {
    if (window.gameController) {
        window.gameController.restartGame();
    }
}

function showMenu() {
    if (window.gameController) {
        window.gameController.showMenu();
    }
}

function hideMenu() {
    if (window.gameController) {
        window.gameController.hideMenu();
    }
}

function returnToStart() {
    if (window.gameController) {
        window.gameController.returnToStart();
    }
}

// Initialize game controller when page loads
$(document).ready(() => {
    if (window.gameData) {
        window.gameController = new GameController();
    }
});
