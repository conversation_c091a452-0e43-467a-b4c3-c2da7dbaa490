-- Insert sample novels
INSERT INTO novels (title, description, author, cover_image_url, total_chapters, is_completed) VALUES
('The Mysterious Academy', 'A thrilling adventure in a magical academy where students discover ancient secrets and face mysterious challenges.', 'Game Designer', '/images/academy_cover.jpg', 5, false),
('Digital Dreams', 'A cyberpunk story set in a futuristic world where reality and virtual worlds collide.', 'Game Designer', '/images/digital_cover.jpg', 3, false);

-- Insert sample characters with emotional images
INSERT INTO characters (name, description, avatar_url, emotional_images, character_type, voice_url, personality_traits) VALUES
('<PERSON>', 'The main protagonist, a curious and brave student with a mysterious past.', '/images/characters/alex_neutral.png',
 '{"NEUTRAL":"/images/characters/alex_neutral.png","EXCITED":"/images/characters/alex_excited.png","SAD":"/images/characters/alex_sad.png","ANGRY":"/images/characters/alex_angry.png","SURPRISED":"/images/characters/alex_surprised.png"}',
 'PROTAGONIST', '/audio/voices/alex.mp3', 'Curious, brave, determined'),

('Professor <PERSON>', 'A wise and mysterious teacher with hidden knowledge of the academy''s secrets.', '/images/characters/morgan_neutral.png',
 '{"NEUTRAL":"/images/characters/morgan_neutral.png","WISE":"/images/characters/morgan_wise.png","CONCERNED":"/images/characters/morgan_concerned.png","MYSTERIOUS":"/images/characters/morgan_mysterious.png"}',
 'NPC', '/audio/voices/morgan.mp3', 'Wise, mysterious, protective'),

('Luna', 'Alex''s best friend, a talented magic user with a cheerful personality.', '/images/characters/luna_neutral.png',
 '{"NEUTRAL":"/images/characters/luna_neutral.png","HAPPY":"/images/characters/luna_happy.png","WORRIED":"/images/characters/luna_worried.png","EXCITED":"/images/characters/luna_excited.png"}',
 'NPC', '/audio/voices/luna.mp3', 'Cheerful, loyal, talented'),

('The Narrator', 'The storyteller who guides the reader through the adventure.', null, '{}', 'NARRATOR', '/audio/voices/narrator.mp3', 'Omniscient, guiding'),

('Shadow Figure', 'A mysterious antagonist whose true identity remains hidden.', '/images/characters/shadow_neutral.png',
 '{"NEUTRAL":"/images/characters/shadow_neutral.png","MENACING":"/images/characters/shadow_menacing.png","ANGRY":"/images/characters/shadow_angry.png"}',
 'NPC', '/audio/voices/shadow.mp3', 'Mysterious, threatening, powerful');

-- Insert chapters for The Mysterious Academy
INSERT INTO chapters (title, description, chapter_number, cover_image_url, is_completed, is_unlocked, estimated_duration_minutes, difficulty_level, novel_id) VALUES
('Arrival at the Academy', 'Alex arrives at the mysterious academy and begins to explore its secrets.', 1, '/images/chapters/chapter1.jpg', false, true, 15, 'EASY', 1),
('The First Lesson', 'Alex attends their first magic lesson and discovers their unique abilities.', 2, '/images/chapters/chapter2.jpg', false, false, 20, 'NORMAL', 1),
('Hidden Passages', 'Alex and Luna discover secret passages beneath the academy.', 3, '/images/chapters/chapter3.jpg', false, false, 25, 'NORMAL', 1),
('The Ancient Library', 'The duo explores the forbidden ancient library and uncovers dark secrets.', 4, '/images/chapters/chapter4.jpg', false, false, 30, 'HARD', 1),
('Final Confrontation', 'Alex faces the shadow figure in an epic final battle.', 5, '/images/chapters/chapter5.jpg', false, false, 35, 'HARD', 1);

-- Insert backgrounds for Chapter 1 (at least 2 per chapter)
INSERT INTO backgrounds (name, description, scene_number, background_image_url, background_music_url, ambient_sound_url, weather_effect, time_of_day, chapter_id) VALUES
('Academy Entrance', 'The grand entrance of the mysterious academy with towering spires and ancient architecture.', 1, '/images/academy_entrance.png', '/audio/music/academy_theme.mp3', '/audio/ambient/wind.mp3', null, 'MORNING', 1),
('Academy Courtyard', 'A beautiful courtyard filled with magical plants and floating crystals.', 2, '/images/academy_courtyard.png', '/audio/music/peaceful_theme.mp3', '/audio/ambient/birds.mp3', null, 'MORNING', 1);

-- Insert backgrounds for Chapter 2
INSERT INTO backgrounds (name, description, scene_number, background_image_url, background_music_url, ambient_sound_url, weather_effect, time_of_day, chapter_id) VALUES
('Magic Classroom', 'A spacious classroom filled with magical artifacts and floating books.', 1, '/images/classroom.png', '/audio/music/lesson_theme.mp3', '/audio/ambient/magic_hum.mp3', null, 'AFTERNOON', 2),
('Practice Arena', 'An outdoor arena where students practice their magical abilities.', 2, '/images/practice_arena.png', '/audio/music/training_theme.mp3', '/audio/ambient/magic_sparks.mp3', null, 'AFTERNOON', 2);

-- Insert backgrounds for Chapter 3
INSERT INTO backgrounds (name, description, scene_number, background_image_url, background_music_url, ambient_sound_url, weather_effect, time_of_day, chapter_id) VALUES
('Underground Tunnel', 'A dark, mysterious tunnel beneath the academy filled with ancient magic.', 1, '/images/underground_tunnel.png', '/audio/music/mystery_theme.mp3', '/audio/ambient/dripping.mp3', null, 'NIGHT', 3),
('Secret Chamber', 'A hidden chamber containing ancient artifacts and mysterious symbols.', 2, '/images/secret_chamber.png', '/audio/music/ancient_theme.mp3', '/audio/ambient/mystical_hum.mp3', null, 'NIGHT', 3);

-- Insert backgrounds for Chapter 4
INSERT INTO backgrounds (name, description, scene_number, background_image_url, background_music_url, ambient_sound_url, weather_effect, time_of_day, chapter_id) VALUES
('Ancient Library', 'A vast library with towering bookshelves and floating magical tomes.', 1, '/images/library.png', '/audio/music/library_theme.mp3', '/audio/ambient/pages_turning.mp3', null, 'EVENING', 4),
('Forbidden Section', 'The restricted area of the library containing dangerous knowledge.', 2, '/images/forbidden_section.png', '/audio/music/dark_theme.mp3', '/audio/ambient/whispers.mp3', null, 'EVENING', 4);

-- Insert backgrounds for Chapter 5
INSERT INTO backgrounds (name, description, scene_number, background_image_url, background_music_url, ambient_sound_url, weather_effect, time_of_day, chapter_id) VALUES
('Headmaster Office', 'The grand office of the academy''s headmaster, filled with powerful artifacts.', 1, '/images/headmaster_office.png', '/audio/music/tension_theme.mp3', '/audio/ambient/clock_ticking.mp3', 'STORM', 'NIGHT', 5),
('Rooftop Battle', 'The academy''s highest tower where the final confrontation takes place.', 2, '/images/rooftop_battle.png', '/audio/music/battle_theme.mp3', '/audio/ambient/thunder.mp3', 'STORM', 'NIGHT', 5);

-- Insert dialogues for Academy Entrance (Background ID 1)
INSERT INTO dialogues (text, sequence_order, dialogue_type, emotion, voice_file_url, display_duration_ms, auto_advance, character_position, character_id, background_id) VALUES
('Welcome to the Mysterious Academy, where knowledge and magic intertwine in ways beyond imagination.', 0, 'NARRATION', 'NEUTRAL', '/audio/dialogue/narrator_01.mp3', null, false, 'CENTER', 4, 1),
('Wow, this place is incredible! I can''t believe I actually got accepted here.', 1, 'SPEECH', 'EXCITED', '/audio/dialogue/alex_01.mp3', null, false, 'CENTER', 1, 1),
('The academy has stood for over a thousand years, young one. Its secrets run deeper than you can imagine.', 2, 'SPEECH', 'WISE', '/audio/dialogue/morgan_01.mp3', null, false, 'RIGHT', 2, 1),
('I''m ready to learn everything! When do we start?', 3, 'SPEECH', 'EXCITED', '/audio/dialogue/alex_02.mp3', null, false, 'CENTER', 1, 1);

-- Insert dialogues for Academy Courtyard (Background ID 2)
INSERT INTO dialogues (text, sequence_order, dialogue_type, emotion, voice_file_url, display_duration_ms, auto_advance, character_position, character_id, background_id) VALUES
('The courtyard buzzes with magical energy as students practice their spells.', 0, 'NARRATION', 'NEUTRAL', '/audio/dialogue/narrator_02.mp3', null, false, 'CENTER', 4, 2),
('Hi there! You must be the new student. I''m Luna!', 1, 'SPEECH', 'HAPPY', '/audio/dialogue/luna_01.mp3', null, false, 'LEFT', 3, 2),
('Nice to meet you, Luna. I''m Alex. This place is amazing!', 2, 'SPEECH', 'HAPPY', '/audio/dialogue/alex_03.mp3', null, false, 'RIGHT', 1, 2),
('Want me to show you around? There are so many cool places to explore!', 3, 'SPEECH', 'EXCITED', '/audio/dialogue/luna_02.mp3', null, false, 'LEFT', 3, 2);

-- Insert dialogues for Magic Classroom (Background ID 3)
INSERT INTO dialogues (text, sequence_order, dialogue_type, emotion, voice_file_url, display_duration_ms, auto_advance, character_position, character_id, background_id) VALUES
('Today we begin your first lesson in the fundamental arts of magic.', 0, 'SPEECH', 'WISE', '/audio/dialogue/morgan_02.mp3', null, false, 'CENTER', 2, 3),
('Focus your energy, Alex. Feel the magic flowing through you.', 1, 'SPEECH', 'CONCERNED', '/audio/dialogue/morgan_03.mp3', null, false, 'CENTER', 2, 3),
('I can feel something... it''s warm and tingly!', 2, 'SPEECH', 'SURPRISED', '/audio/dialogue/alex_04.mp3', null, false, 'LEFT', 1, 3),
('Excellent! Your magical potential is stronger than most.', 3, 'SPEECH', 'WISE', '/audio/dialogue/morgan_04.mp3', null, false, 'CENTER', 2, 3);

-- Insert dialogues for Practice Arena (Background ID 4)
INSERT INTO dialogues (text, sequence_order, dialogue_type, emotion, voice_file_url, display_duration_ms, auto_advance, character_position, character_id, background_id) VALUES
('The practice arena echoes with the sounds of magical training.', 0, 'NARRATION', 'NEUTRAL', '/audio/dialogue/narrator_03.mp3', null, false, 'CENTER', 4, 4),
('Watch this, Alex! I''ve been practicing this spell for weeks!', 1, 'SPEECH', 'EXCITED', '/audio/dialogue/luna_03.mp3', null, false, 'LEFT', 3, 4),
('That was incredible, Luna! How did you make those lights dance like that?', 2, 'SPEECH', 'EXCITED', '/audio/dialogue/alex_05.mp3', null, false, 'RIGHT', 1, 4),
('It''s all about visualization and emotional connection to the magic.', 3, 'SPEECH', 'HAPPY', '/audio/dialogue/luna_04.mp3', null, false, 'LEFT', 3, 4);

-- Insert dialogues for Underground Tunnel (Background ID 5)
INSERT INTO dialogues (text, sequence_order, dialogue_type, emotion, voice_file_url, display_duration_ms, auto_advance, character_position, character_id, background_id) VALUES
('The ancient tunnels beneath the academy hold secrets that few have discovered.', 0, 'NARRATION', 'NEUTRAL', '/audio/dialogue/narrator_04.mp3', null, false, 'CENTER', 4, 5),
('This place gives me the creeps. Are you sure we should be down here?', 1, 'SPEECH', 'WORRIED', '/audio/dialogue/luna_05.mp3', null, false, 'LEFT', 3, 5),
('We have to find out what''s been causing those strange noises at night.', 2, 'SPEECH', 'NEUTRAL', '/audio/dialogue/alex_06.mp3', null, false, 'RIGHT', 1, 5),
('Look at these symbols on the wall... they''re glowing!', 3, 'SPEECH', 'SURPRISED', '/audio/dialogue/alex_07.mp3', null, false, 'RIGHT', 1, 5);

-- Insert remaining dialogues for other backgrounds (keeping it concise for space)
-- Secret Chamber (Background ID 6)
INSERT INTO dialogues (text, sequence_order, dialogue_type, emotion, voice_file_url, display_duration_ms, auto_advance, character_position, character_id, background_id) VALUES
('The chamber reveals ancient artifacts of immense power.', 0, 'NARRATION', 'NEUTRAL', '/audio/dialogue/narrator_05.mp3', null, false, 'CENTER', 4, 6),
('These artifacts... they''re calling to me somehow.', 1, 'SPEECH', 'SURPRISED', '/audio/dialogue/alex_08.mp3', null, false, 'CENTER', 1, 6);

-- Ancient Library (Background ID 7)
INSERT INTO dialogues (text, sequence_order, dialogue_type, emotion, voice_file_url, display_duration_ms, auto_advance, character_position, character_id, background_id) VALUES
('The ancient library contains knowledge from ages past.', 0, 'NARRATION', 'NEUTRAL', '/audio/dialogue/narrator_06.mp3', null, false, 'CENTER', 4, 7),
('These books... they''re writing themselves!', 1, 'SPEECH', 'SURPRISED', '/audio/dialogue/alex_09.mp3', null, false, 'CENTER', 1, 7);

-- Forbidden Section (Background ID 8)
INSERT INTO dialogues (text, sequence_order, dialogue_type, emotion, voice_file_url, display_duration_ms, auto_advance, character_position, character_id, background_id) VALUES
('You should not have come here, young one.', 0, 'SPEECH', 'MENACING', '/audio/dialogue/shadow_01.mp3', null, false, 'CENTER', 5, 8),
('Who are you? What do you want?', 1, 'SPEECH', 'ANGRY', '/audio/dialogue/alex_10.mp3', null, false, 'LEFT', 1, 8);

-- Headmaster Office (Background ID 9)
INSERT INTO dialogues (text, sequence_order, dialogue_type, emotion, voice_file_url, display_duration_ms, auto_advance, character_position, character_id, background_id) VALUES
('The final confrontation begins as storm clouds gather.', 0, 'NARRATION', 'NEUTRAL', '/audio/dialogue/narrator_07.mp3', null, false, 'CENTER', 4, 9),
('You cannot stop what has already begun!', 1, 'SPEECH', 'ANGRY', '/audio/dialogue/shadow_02.mp3', null, false, 'RIGHT', 5, 9);

-- Rooftop Battle (Background ID 10)
INSERT INTO dialogues (text, sequence_order, dialogue_type, emotion, voice_file_url, display_duration_ms, auto_advance, character_position, character_id, background_id) VALUES
('This ends now!', 0, 'SPEECH', 'ANGRY', '/audio/dialogue/alex_11.mp3', null, false, 'LEFT', 1, 10),
('The academy''s fate hangs in the balance as the final battle rages.', 1, 'NARRATION', 'NEUTRAL', '/audio/dialogue/narrator_08.mp3', null, false, 'CENTER', 4, 10);

-- Insert game actions for each background
-- Academy Entrance (Background ID 1)
INSERT INTO game_actions (action_type, sequence_order, target_element, action_value, duration_ms, delay_before_ms, trigger_condition, is_blocking, description, background_id) VALUES
('PLAY_MUSIC', 0, 'background_music', '/audio/music/academy_theme.mp3', null, 0, 'SCENE_START', false, 'Play academy entrance theme', 1),
('FADE_IN', 1, 'scene', '2000', 2000, 0, 'SCENE_START', false, 'Fade in the academy entrance', 1),
('PLAY_AMBIENT', 2, 'ambient_sound', '/audio/ambient/wind.mp3', null, 1000, 'SCENE_START', false, 'Play wind ambient sound', 1),
('NEXT_SCENE', 3, null, null, null, 0, 'DIALOGUE_END', false, 'Move to next scene after all dialogues', 1);

-- Academy Courtyard (Background ID 2)
INSERT INTO game_actions (action_type, sequence_order, target_element, action_value, duration_ms, delay_before_ms, trigger_condition, is_blocking, description, background_id) VALUES
('PLAY_MUSIC', 0, 'background_music', '/audio/music/peaceful_theme.mp3', null, 0, 'SCENE_START', false, 'Play peaceful courtyard theme', 2),
('PLAY_AMBIENT', 1, 'ambient_sound', '/audio/ambient/birds.mp3', null, 500, 'SCENE_START', false, 'Play birds chirping', 2),
('NEXT_CHAPTER', 2, null, null, null, 0, 'DIALOGUE_END', false, 'Move to next chapter after dialogues', 2);

-- Magic Classroom (Background ID 3)
INSERT INTO game_actions (action_type, sequence_order, target_element, action_value, duration_ms, delay_before_ms, trigger_condition, is_blocking, description, background_id) VALUES
('PLAY_MUSIC', 0, 'background_music', '/audio/music/lesson_theme.mp3', null, 0, 'SCENE_START', false, 'Play lesson theme', 3),
('PLAY_AMBIENT', 1, 'ambient_sound', '/audio/ambient/magic_hum.mp3', null, 0, 'SCENE_START', false, 'Play magical humming', 3),
('NEXT_SCENE', 2, null, null, null, 0, 'DIALOGUE_END', false, 'Move to practice arena', 3);

-- Practice Arena (Background ID 4)
INSERT INTO game_actions (action_type, sequence_order, target_element, action_value, duration_ms, delay_before_ms, trigger_condition, is_blocking, description, background_id) VALUES
('PLAY_MUSIC', 0, 'background_music', '/audio/music/training_theme.mp3', null, 0, 'SCENE_START', false, 'Play training theme', 4),
('PLAY_AMBIENT', 1, 'ambient_sound', '/audio/ambient/magic_sparks.mp3', null, 0, 'SCENE_START', false, 'Play magic sparks sound', 4),
('NEXT_CHAPTER', 2, null, null, null, 0, 'DIALOGUE_END', false, 'Move to next chapter', 4);

-- Continue with remaining backgrounds...
-- Underground Tunnel (Background ID 5)
INSERT INTO game_actions (action_type, sequence_order, target_element, action_value, duration_ms, delay_before_ms, trigger_condition, is_blocking, description, background_id) VALUES
('PLAY_MUSIC', 0, 'background_music', '/audio/music/mystery_theme.mp3', null, 0, 'SCENE_START', false, 'Play mystery theme', 5),
('PLAY_AMBIENT', 1, 'ambient_sound', '/audio/ambient/dripping.mp3', null, 0, 'SCENE_START', false, 'Play dripping sounds', 5),
('NEXT_SCENE', 2, null, null, null, 0, 'DIALOGUE_END', false, 'Move to secret chamber', 5);

-- Secret Chamber (Background ID 6)
INSERT INTO game_actions (action_type, sequence_order, target_element, action_value, duration_ms, delay_before_ms, trigger_condition, is_blocking, description, background_id) VALUES
('PLAY_MUSIC', 0, 'background_music', '/audio/music/ancient_theme.mp3', null, 0, 'SCENE_START', false, 'Play ancient theme', 6),
('PLAY_AMBIENT', 1, 'ambient_sound', '/audio/ambient/mystical_hum.mp3', null, 0, 'SCENE_START', false, 'Play mystical humming', 6),
('NEXT_CHAPTER', 2, null, null, null, 0, 'DIALOGUE_END', false, 'Move to next chapter', 6);

-- Ancient Library (Background ID 7)
INSERT INTO game_actions (action_type, sequence_order, target_element, action_value, duration_ms, delay_before_ms, trigger_condition, is_blocking, description, background_id) VALUES
('PLAY_MUSIC', 0, 'background_music', '/audio/music/library_theme.mp3', null, 0, 'SCENE_START', false, 'Play library theme', 7),
('PLAY_AMBIENT', 1, 'ambient_sound', '/audio/ambient/pages_turning.mp3', null, 0, 'SCENE_START', false, 'Play pages turning', 7),
('NEXT_SCENE', 2, null, null, null, 0, 'DIALOGUE_END', false, 'Move to forbidden section', 7);

-- Forbidden Section (Background ID 8)
INSERT INTO game_actions (action_type, sequence_order, target_element, action_value, duration_ms, delay_before_ms, trigger_condition, is_blocking, description, background_id) VALUES
('PLAY_MUSIC', 0, 'background_music', '/audio/music/dark_theme.mp3', null, 0, 'SCENE_START', false, 'Play dark theme', 8),
('PLAY_AMBIENT', 1, 'ambient_sound', '/audio/ambient/whispers.mp3', null, 0, 'SCENE_START', false, 'Play whispers', 8),
('NEXT_CHAPTER', 2, null, null, null, 0, 'DIALOGUE_END', false, 'Move to final chapter', 8);

-- Headmaster Office (Background ID 9)
INSERT INTO game_actions (action_type, sequence_order, target_element, action_value, duration_ms, delay_before_ms, trigger_condition, is_blocking, description, background_id) VALUES
('PLAY_MUSIC', 0, 'background_music', '/audio/music/tension_theme.mp3', null, 0, 'SCENE_START', false, 'Play tension theme', 9),
('PLAY_AMBIENT', 1, 'ambient_sound', '/audio/ambient/clock_ticking.mp3', null, 0, 'SCENE_START', false, 'Play clock ticking', 9),
('WEATHER_EFFECT', 2, 'weather', 'STORM', null, 0, 'SCENE_START', false, 'Start storm effect', 9),
('NEXT_SCENE', 3, null, null, null, 0, 'DIALOGUE_END', false, 'Move to final battle', 9);

-- Rooftop Battle (Background ID 10)
INSERT INTO game_actions (action_type, sequence_order, target_element, action_value, duration_ms, delay_before_ms, trigger_condition, is_blocking, description, background_id) VALUES
('PLAY_MUSIC', 0, 'background_music', '/audio/music/battle_theme.mp3', null, 0, 'SCENE_START', false, 'Play battle theme', 10),
('PLAY_AMBIENT', 1, 'ambient_sound', '/audio/ambient/thunder.mp3', null, 0, 'SCENE_START', false, 'Play thunder sounds', 10),
('WEATHER_EFFECT', 2, 'weather', 'STORM', null, 0, 'SCENE_START', false, 'Continue storm effect', 10),
('END_GAME', 3, null, null, null, 0, 'DIALOGUE_END', false, 'End the game', 10);