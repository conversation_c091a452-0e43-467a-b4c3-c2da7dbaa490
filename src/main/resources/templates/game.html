<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${gameState.novelTitle} + ' - PPT Game'">PPT Game</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #000;
            color: #fff;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        .game-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        }
        .weather-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 2;
        }
        .weather-overlay.storm::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                90deg,
                transparent,
                transparent 2px,
                rgba(255,255,255,0.1) 2px,
                rgba(255,255,255,0.1) 4px
            );
            animation: rain 0.3s linear infinite;
        }
        @keyframes rain {
            0% { transform: translateX(-10px); }
            100% { transform: translateX(10px); }
        }
        .time-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 2;
        }
        .time-overlay.morning {
            background: linear-gradient(to bottom, rgba(255, 223, 186, 0.1) 0%, transparent 50%);
        }
        .time-overlay.afternoon {
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        }
        .time-overlay.evening {
            background: linear-gradient(to bottom, rgba(255, 165, 0, 0.2) 0%, transparent 50%);
        }
        .time-overlay.night {
            background: linear-gradient(to bottom, rgba(0, 0, 50, 0.4) 0%, transparent 50%);
        }
        .character-area {
            position: absolute;
            bottom: 20%;
            left: 0;
            width: 100%;
            height: 60%;
            z-index: 3;
            pointer-events: none;
        }
        .character-display {
            position: absolute;
            bottom: 0;
            height: 100%;
            transition: all 0.5s ease;
        }
        .character-display.left {
            left: 10%;
        }
        .character-display.center {
            left: 50%;
            transform: translateX(-50%);
        }
        .character-display.right {
            right: 10%;
        }
        .character-image {
            max-height: 100%;
            max-width: 300px;
            object-fit: contain;
        }
        .ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
            pointer-events: none;
        }
        .ui-overlay > * {
            pointer-events: auto;
        }
        .top-bar {
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
            padding: 1rem 2rem;
        }
        .progress-bar-custom {
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            transition: width 0.3s ease;
        }
        .dialogue-container {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 2rem;
            padding-bottom: 6rem; /* Make room for buttons */
        }
        .dialogue-box {
            background: rgba(0, 0, 0, 0.85);
            border-radius: 15px;
            padding: 1.5rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .dialogue-box.narration {
            border-left: 4px solid #4ecdc4;
        }
        .dialogue-box.speech {
            border-left: 4px solid #ff6b6b;
        }
        .character-name {
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        .dialogue-text {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #fff;
        }
        .control-panel {
            position: absolute;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 20;
        }

        .menu-button-container {
            position: absolute;
            bottom: 2rem;
            left: 2rem;
            z-index: 20;
        }
        .btn-game {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.4);
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            position: relative;
            z-index: 25;
        }
        .btn-game:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.15);
            border-color: #4ecdc4;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        .btn-game:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .btn-game:active {
            transform: translateY(0);
        }
        .btn-next {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border-color: #4ecdc4;
        }
        .btn-prev {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            border-color: #ff6b6b;
        }
        .menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            display: none;
        }
        .menu-content {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .emotion-indicator {
            position: absolute;
            top: -10px;
            right: 20px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
        .emotion-indicator.excited {
            background: #ffeb3b;
            animation: pulse 1s infinite;
        }
        .emotion-indicator.sad {
            background: #2196f3;
        }
        .emotion-indicator.angry {
            background: #f44336;
        }
        .emotion-indicator.neutral {
            background: #9e9e9e;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Background -->
        <div class="game-background" 
             th:style="'background-image: url(' + @{${gameState.currentBackground.backgroundImageUrl}} + ');'">
            
            <!-- Weather Effects -->
            <div class="weather-overlay" 
                 th:if="${gameState.currentBackground.weatherEffect}"
                 th:classappend="${gameState.currentBackground.weatherEffect?.name()?.toLowerCase()}">
            </div>
            
            <!-- Time Effects -->
            <div class="time-overlay" 
                 th:classappend="${gameState.currentBackground.timeOfDay?.name()?.toLowerCase()}">
            </div>
        </div>
        
        <!-- Character Display -->
        <div class="character-area">
            <div th:each="dialogue : ${gameState.dialogues}" 
                 th:if="${dialogue.sequenceOrder == gameState.currentDialogueIndex}"
                 class="character-display"
                 th:classappend="${dialogue.characterPosition?.name()?.toLowerCase()}">
                
                <div th:if="${dialogue.character.avatarUrl}" class="character-avatar">
                    <img th:src="@{${dialogue.character.avatarUrl}}" 
                         th:alt="${dialogue.character.name}"
                         class="character-image"
                         th:id="'character-' + ${dialogue.character.id}">
                </div>
            </div>
        </div>
        
        <!-- UI Overlay -->
        <div class="ui-overlay">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 th:text="${gameState.novelTitle}" class="mb-1">Novel Title</h4>
                        <small class="text-muted">
                            Chapter <span th:text="${gameState.currentChapter}">1</span> - 
                            Scene <span th:text="${gameState.currentScene}">1</span>
                        </small>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <div class="progress-bar-custom flex-grow-1 me-2">
                                <div class="progress-fill" 
                                     th:style="'width: ' + ${gameState.progress.overallProgress} + '%'"></div>
                            </div>
                            <small class="text-muted" 
                                   th:text="${#numbers.formatDecimal(gameState.progress.overallProgress, 1, 1)} + '%'">0%</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Dialogue Box -->
            <div class="dialogue-container">
                <div th:each="dialogue : ${gameState.dialogues}"
                     th:if="${dialogue.sequenceOrder == gameState.currentDialogueIndex}"
                     class="dialogue-box position-relative"
                     th:classappend="${dialogue.dialogueType?.name()?.toLowerCase()}">
                    
                    <div class="character-name" 
                         th:if="${dialogue.dialogueType?.name() != 'NARRATION'}"
                         th:text="${dialogue.character.name}">Character Name</div>
                    
                    <div class="dialogue-text" th:text="${dialogue.text}">
                        Dialogue text goes here...
                    </div>
                    
                    <div class="emotion-indicator" 
                         th:if="${dialogue.emotion}"
                         th:classappend="${dialogue.emotion?.name()?.toLowerCase()}">
                    </div>
                </div>
            </div>
            
            <!-- Control Panel - Right Corner -->
            <div class="control-panel">
                <button class="btn btn-game btn-prev"
                        th:disabled="${!gameState.hasPrevious}"
                        id="prevBtn">
                    ← Previous
                </button>

                <button class="btn btn-game btn-next"
                        th:disabled="${!gameState.hasNext}"
                        id="nextBtn">
                    Next →
                </button>
            </div>

            <!-- Menu Button - Left Corner -->
            <div class="menu-button-container">
                <button class="btn btn-game" id="menuBtn">
                    <i class="fas fa-bars"></i> Menu
                </button>
            </div>
        </div>
        
        <!-- Menu Modal -->
        <div class="menu-overlay" id="gameMenu">
            <div class="d-flex align-items-center justify-content-center h-100">
                <div class="menu-content p-4 text-center">
                    <h3 class="text-info mb-4">Game Menu</h3>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-light" id="saveBtn">Save Game</button>
                        <button class="btn btn-outline-light" id="restartBtn">Restart Game</button>
                        <button class="btn btn-outline-light" id="returnBtn">Return to Start</button>
                        <button class="btn btn-secondary" id="closeMenuBtn">Close Menu</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Audio Elements -->
    <audio id="backgroundMusic" loop></audio>
    <audio id="ambientSound" loop></audio>
    <audio id="voiceAudio"></audio>
    
    <!-- Game Data -->
    <script th:inline="javascript">
        window.gameData = {
            sessionId: /*[[${gameState.sessionId}]]*/ '',
            currentDialogueIndex: /*[[${gameState.currentDialogueIndex}]]*/ 0,
            totalDialogues: /*[[${gameState.progress.totalDialoguesInScene}]]*/ 0,
            hasNext: /*[[${gameState.hasNext}]]*/ false,
            hasPrevious: /*[[${gameState.hasPrevious}]]*/ false,
            currentBackground: /*[[${gameState.currentBackground}]]*/ {},
            dialogues: /*[[${gameState.dialogues}]]*/ [],
            gameActions: /*[[${gameState.gameActions}]]*/ []
        };
    </script>
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/js/game.js}"></script>
</body>
</html>
