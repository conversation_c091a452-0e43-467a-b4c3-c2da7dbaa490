<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT Game - Start New Game</title>
    <link th:href="@{/webjars/bootstrap/css/bootstrap.min.css}" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .start-container {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
        }
        .game-title {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }
        .btn-start {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            font-weight: bold;
            transition: transform 0.2s;
        }
        .btn-start:hover {
            transform: translateY(-2px);
        }
        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
        }
        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #4ecdc4;
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(78, 205, 196, 0.25);
        }
        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        .form-label {
            color: white;
            font-weight: 500;
        }
        .form-select option {
            background: #333;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
        <div class="start-container p-5 col-md-6 col-lg-4">
            <div class="text-center mb-4">
                <h1 class="game-title display-4 mb-3">PPT Visual Novel Game</h1>
                <p class="text-light">Welcome to an immersive storytelling experience</p>
            </div>
            
            <form th:action="@{/game/create}" method="post">
                <div class="mb-3">
                    <label for="playerName" class="form-label">Player Name</label>
                    <input type="text" class="form-control" id="playerName" name="playerName" 
                           required placeholder="Enter your name" maxlength="50">
                </div>
                
                <div class="mb-3">
                    <label for="novelId" class="form-label">Select Novel</label>
                    <select class="form-select" id="novelId" name="novelId" required>
                        <option value="1">The Mysterious Academy</option>
                        <option value="2">Digital Dreams</option>
                    </select>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="startChapter" class="form-label">Starting Chapter</label>
                        <input type="number" class="form-control" id="startChapter" name="startChapter" 
                               value="1" min="1" max="5">
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="startScene" class="form-label">Starting Scene</label>
                        <input type="number" class="form-control" id="startScene" name="startScene" 
                               value="1" min="1" max="10">
                    </div>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-start btn-lg text-white">Start Game</button>
                </div>
            </form>
            
            <div th:if="${param.error}" class="alert alert-danger mt-3" role="alert">
                <strong>Error!</strong> Failed to create game session. Please try again.
            </div>
            
            <div class="text-center mt-4">
                <p class="text-muted small">Experience rich storytelling with dynamic characters and immersive backgrounds</p>
            </div>
        </div>
    </div>
    
    <script th:src="@{/webjars/bootstrap/js/bootstrap.bundle.min.js}"></script>
</body>
</html>
