<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT Game - Start New Game</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated background particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.3)"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
            animation: twinkle 3s ease-in-out infinite alternate;
            pointer-events: none;
            z-index: 1;
        }

        @keyframes twinkle {
            0% { opacity: 0.3; }
            100% { opacity: 0.8; }
        }

        .start-container {
            background: rgba(0, 0, 0, 0.85);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 2;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .game-title {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            text-shadow: 0 0 30px rgba(78, 205, 196, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { filter: brightness(1); }
            to { filter: brightness(1.2); }
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .btn-start {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-start::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-start:hover::before {
            left: 100%;
        }

        .btn-start:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(78, 205, 196, 0.4);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #4ecdc4;
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(78, 205, 196, 0.25);
            transform: translateY(-2px);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-label {
            color: white;
            font-weight: 600;
            margin-bottom: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-select option {
            background: #2c3e50;
            color: white;
        }

        .icon-wrapper {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            margin-bottom: 1rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin-top: 2rem;
        }

        .feature-list li {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-list li i {
            color: #4ecdc4;
        }
    </style>
</head>
<body>
    <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100 py-4">
        <div class="start-container p-5 col-md-8 col-lg-6 col-xl-5">
            <div class="text-center mb-4">
                <div class="icon-wrapper mx-auto">
                    <i class="fas fa-gamepad text-white"></i>
                </div>
                <h1 class="game-title display-3 mb-3">PPT Visual Novel</h1>
                <p class="subtitle">Embark on an epic journey through interactive storytelling</p>
            </div>

            <form th:action="@{/game/create}" method="post">
                <div class="mb-4">
                    <label for="playerName" class="form-label">
                        <i class="fas fa-user"></i> Player Name
                    </label>
                    <input type="text" class="form-control form-control-lg" id="playerName" name="playerName"
                           required placeholder="Enter your hero's name" maxlength="50">
                </div>

                <div class="mb-4">
                    <label for="novelId" class="form-label">
                        <i class="fas fa-book"></i> Choose Your Adventure
                    </label>
                    <select class="form-select form-select-lg" id="novelId" name="novelId" required>
                        <option value="">Select a story...</option>
                        <option value="1">🏰 The Mysterious Academy</option>
                        <option value="2">🌆 Digital Dreams</option>
                    </select>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-4">
                        <label for="startChapter" class="form-label">
                            <i class="fas fa-bookmark"></i> Chapter
                        </label>
                        <input type="number" class="form-control form-control-lg" id="startChapter" name="startChapter"
                               value="1" min="1" max="5">
                    </div>

                    <div class="col-md-6 mb-4">
                        <label for="startScene" class="form-label">
                            <i class="fas fa-map-marker-alt"></i> Scene
                        </label>
                        <input type="number" class="form-control form-control-lg" id="startScene" name="startScene"
                               value="1" min="1" max="10">
                    </div>
                </div>

                <div class="d-grid mb-4">
                    <button type="submit" class="btn btn-start btn-lg text-white py-3">
                        <i class="fas fa-play me-2"></i>Begin Adventure
                    </button>
                </div>
            </form>

            <div th:if="${param.error}" class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Oops!</strong> Failed to create game session. Please try again.
            </div>

            <ul class="feature-list">
                <li><i class="fas fa-magic"></i> Immersive storytelling with rich characters</li>
                <li><i class="fas fa-music"></i> Dynamic audio and visual effects</li>
                <li><i class="fas fa-heart"></i> Emotional character interactions</li>
                <li><i class="fas fa-save"></i> Save and resume your progress</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
