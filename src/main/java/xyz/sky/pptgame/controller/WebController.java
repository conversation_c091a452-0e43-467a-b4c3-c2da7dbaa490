package xyz.sky.pptgame.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import xyz.sky.pptgame.dto.CreateSessionRequest;
import xyz.sky.pptgame.dto.GameStateResponse;
import xyz.sky.pptgame.service.GameService;

@Controller
@RequiredArgsConstructor
@Slf4j
public class WebController {
    
    private final GameService gameService;
    
    @GetMapping("/")
    public String index() {
        return "redirect:/game/start";
    }
    
    @GetMapping("/game/start")
    public String gameStart() {
        return "start";
    }
    
    @PostMapping("/game/create")
    public String createGame(@RequestParam Long novelId,
                           @RequestParam String playerName,
                           @RequestParam(defaultValue = "1") Integer startChapter,
                           @RequestParam(defaultValue = "1") Integer startScene) {
        try {
            CreateSessionRequest request = CreateSessionRequest.builder()
                    .novelId(novelId)
                    .playerName(playerName)
                    .startChapter(startChapter)
                    .startScene(startScene)
                    .build();
            
            GameStateResponse gameState = gameService.createSession(request);
            return "redirect:/game/play/" + gameState.getSessionId();
        } catch (Exception e) {
            log.error("Error creating game session", e);
            return "redirect:/game/start?error=true";
        }
    }
    
    @GetMapping("/game/play/{sessionId}")
    public String playGame(@PathVariable String sessionId, Model model) {
        try {
            GameStateResponse gameState = gameService.getGameState(sessionId);
            model.addAttribute("gameState", gameState);
            return "game";
        } catch (Exception e) {
            log.error("Error loading game state", e);
            return "redirect:/game/start?error=true";
        }
    }
    
    @PostMapping("/game/save/{sessionId}")
    @ResponseBody
    public ResponseEntity<String> saveGame(@PathVariable String sessionId) {
        try {
            // The save functionality is already handled by the GameController API
            // This endpoint is for web-specific save operations if needed
            log.info("Save game request for session: {}", sessionId);
            return ResponseEntity.ok("Game saved successfully");
        } catch (Exception e) {
            log.error("Error saving game", e);
            return ResponseEntity.badRequest().body("Failed to save game");
        }
    }
    
    @PostMapping("/game/restart/{sessionId}")
    public String restartGame(@PathVariable String sessionId) {
        try {
            // Redirect to start page for restart
            log.info("Restart game request for session: {}", sessionId);
            return "redirect:/game/start";
        } catch (Exception e) {
            log.error("Error restarting game", e);
            return "redirect:/game/start?error=true";
        }
    }
}
