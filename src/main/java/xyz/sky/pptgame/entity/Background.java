package xyz.sky.pptgame.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "backgrounds")
@Data
@EqualsAndHashCode(exclude = {"chapter", "dialogues", "gameActions"})
@ToString(exclude = {"chapter", "dialogues", "gameActions"})
public class Background {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Background name cannot be blank")
    @Column(nullable = false)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    

    
    @NotNull(message = "Scene number cannot be null")
    @Column(name = "scene_number", nullable = false)
    private Integer sceneNumber;
    
    @Column(name = "background_image_url")
    private String backgroundImageUrl;
    
    @Column(name = "background_music_url")
    private String backgroundMusicUrl;
    
    @Column(name = "ambient_sound_url")
    private String ambientSoundUrl;
    
    @Column(name = "weather_effect")
    @Enumerated(EnumType.STRING)
    private WeatherEffect weatherEffect;
    
    @Column(name = "time_of_day")
    @Enumerated(EnumType.STRING)
    private TimeOfDay timeOfDay;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chapter_id", nullable = false)
    private Chapter chapter;
    
    @OneToMany(mappedBy = "background", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Dialogue> dialogues;
    
    @OneToMany(mappedBy = "background", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<GameAction> gameActions;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum WeatherEffect {
        SUNNY,
        RAINY,
        SNOWY,
        CLOUDY,
        STORMY,
        FOGGY,
        STORM,
        NONE
    }
    
    public enum TimeOfDay {
        DAWN,
        MORNING,
        NOON,
        AFTERNOON,
        EVENING,
        NIGHT,
        MIDNIGHT
    }
}
