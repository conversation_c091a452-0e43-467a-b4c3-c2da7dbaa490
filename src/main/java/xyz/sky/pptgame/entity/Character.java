package xyz.sky.pptgame.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "characters")
@Data
@EqualsAndHashCode(exclude = {"dialogues"})
@ToString(exclude = {"dialogues"})
public class Character {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Character name cannot be blank")
    @Column(nullable = false)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "avatar_url")
    private String avatarUrl;

    @Column(name = "emotional_images", columnDefinition = "TEXT")
    private String emotionalImages; // JSON string storing emotion -> image URL mapping

    @Column(name = "character_type")
    @Enumerated(EnumType.STRING)
    private CharacterType characterType = CharacterType.NPC;
    
    @Column(name = "voice_url")
    private String voiceUrl;
    
    @Column(name = "personality_traits", columnDefinition = "TEXT")
    private String personalityTraits;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @OneToMany(mappedBy = "character", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Dialogue> dialogues;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum CharacterType {
        PROTAGONIST,
        NPC,
        NARRATOR
    }
}
