package xyz.sky.pptgame.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xyz.sky.pptgame.dto.*;
import xyz.sky.pptgame.entity.*;
import xyz.sky.pptgame.entity.Character;
import xyz.sky.pptgame.repository.*;
import java.util.List;
import xyz.sky.pptgame.exception.GameException;

import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class GameService {
    
    private final GameSessionRepository gameSessionRepository;
    private final NovelRepository novelRepository;
    private final ChapterRepository chapterRepository;
    private final BackgroundRepository backgroundRepository;
    private final DialogueRepository dialogueRepository;
    private final GameActionRepository gameActionRepository;
    
    /**
     * Create a new game session
     */
    public GameStateResponse createSession(CreateSessionRequest request) {
        log.info("Creating new game session for novel ID: {}", request.getNovelId());
        
        Novel novel = novelRepository.findById(request.getNovelId())
                .orElseThrow(() -> new RuntimeException("Novel not found with ID: " + request.getNovelId()));
        
        // Check if resuming existing session
        if (request.getExistingSessionId() != null) {
            Optional<GameSession> existingSession = gameSessionRepository.findBySessionId(request.getExistingSessionId());
            if (existingSession.isPresent()) {
                return getGameState(request.getExistingSessionId());
            }
        }
        
        // Find the starting chapter
        Chapter startChapter = chapterRepository
                .findByNovelIdAndChapterNumber(request.getNovelId(), request.getStartChapter())
                .orElseThrow(() -> new GameException("Starting chapter not found", "CHAPTER_NOT_FOUND"));

        // Find the starting background
        Background startBackground = backgroundRepository
                .findByChapterIdAndSceneNumber(startChapter.getId(), request.getStartScene())
                .orElseThrow(() -> new GameException("Starting background not found", "BACKGROUND_NOT_FOUND"));
        
        // Create new session
        GameSession session = new GameSession();
        session.setSessionId(UUID.randomUUID().toString());
        session.setPlayerName(request.getPlayerName());
        session.setNovel(novel);
        session.setCurrentChapter(startChapter);
        session.setCurrentBackground(startBackground);
        session.setCurrentChapterNumber(request.getStartChapter());
        session.setCurrentScene(request.getStartScene());
        session.setCurrentDialogueIndex(0);
        session.setCurrentActionIndex(0);
        session.setSessionState(GameSession.SessionState.ACTIVE);
        
        session = gameSessionRepository.save(session);
        
        log.info("Created game session with ID: {}", session.getSessionId());
        return this.getGameState(session.getSessionId());
    }
    
    /**
     * Get current game state
     */
    @Transactional(readOnly = true)
    public GameStateResponse getGameState(String sessionId) {
        GameSession session = gameSessionRepository.findBySessionIdWithDetails(sessionId)
                .orElseThrow(() -> new RuntimeException("Game session not found: " + sessionId));
        
        Background currentBackground = session.getCurrentBackground();
        Chapter currentChapter = session.getCurrentChapter();

        if (currentBackground == null || currentChapter == null) {
            // Fallback: find chapter and background by numbers
            if (currentChapter == null) {
                currentChapter = chapterRepository
                        .findByNovelIdAndChapterNumber(
                                session.getNovel().getId(),
                                session.getCurrentChapterNumber())
                        .orElseThrow(() -> new GameException("Current chapter not found", "CHAPTER_NOT_FOUND"));
            }

            if (currentBackground == null) {
                currentBackground = backgroundRepository
                        .findByChapterIdAndSceneNumber(currentChapter.getId(), session.getCurrentScene())
                        .orElseThrow(() -> new GameException("Current background not found", "BACKGROUND_NOT_FOUND"));
            }
        }
        
        // Get dialogues and actions for current background
        List<Dialogue> dialogues = dialogueRepository.findByBackgroundIdOrderBySequenceOrderAsc(currentBackground.getId());
        List<GameAction> gameActions = gameActionRepository.findByBackgroundIdOrderBySequenceOrderAsc(currentBackground.getId());
        
        // Check navigation availability
        boolean hasNext = hasNextScene(session.getNovel().getId(), session.getCurrentChapterNumber(), session.getCurrentScene());
        boolean hasPrevious = hasPreviousScene(session.getNovel().getId(), session.getCurrentChapterNumber(), session.getCurrentScene());
        
        // Build progress information
        GameStateResponse.GameProgressDto progress = buildProgressInfo(session);
        
        return GameStateResponse.builder()
                .sessionId(sessionId)
                .novelId(session.getNovel().getId())
                .novelTitle(session.getNovel().getTitle())
                .currentChapter(session.getCurrentChapterNumber())
                .currentScene(session.getCurrentScene())
                .currentDialogueIndex(session.getCurrentDialogueIndex())
                .currentActionIndex(session.getCurrentActionIndex())
                .currentBackground(mapToBackgroundDto(currentBackground))
                .dialogues(dialogues.stream().map(this::mapToDialogueDto).collect(Collectors.toList()))
                .gameActions(gameActions.stream().map(this::mapToGameActionDto).collect(Collectors.toList()))
                .progress(progress)
                .hasNext(hasNext)
                .hasPrevious(hasPrevious)
                .build();
    }
    
    /**
     * Execute a game action
     */
    public GameStateResponse executeAction(GameActionRequest request) {
        log.info("Executing action: {} for session: {}", request.getActionType(), request.getSessionId());
        
        GameSession session = gameSessionRepository.findBySessionId(request.getSessionId())
                .orElseThrow(() -> new RuntimeException("Game session not found: " + request.getSessionId()));
        
        switch (request.getActionType()) {
            case NEXT_DIALOGUE:
                return nextDialogue(session);
            case PREVIOUS_DIALOGUE:
                return previousDialogue(session);
            case NEXT_SCENE:
                return nextScene(session);
            case PREVIOUS_SCENE:
                return previousScene(session);
            case JUMP_TO_CHAPTER:
                return jumpToChapter(session, (Integer) request.getActionData());
            case JUMP_TO_SCENE:
                return jumpToScene(session, (Integer) request.getActionData());
            case SAVE_GAME:
                return saveGame(session);
            case PAUSE_GAME:
                return pauseGame(session);
            case RESUME_GAME:
                return resumeGame(session);
            default:
                throw new RuntimeException("Unsupported action type: " + request.getActionType());
        }
    }
    
    private GameStateResponse nextDialogue(GameSession session) {
        List<Dialogue> dialogues = dialogueRepository.findByBackgroundIdOrderBySequenceOrderAsc(
                session.getCurrentBackground().getId());
        
        if (session.getCurrentDialogueIndex() < dialogues.size() - 1) {
            session.setCurrentDialogueIndex(session.getCurrentDialogueIndex() + 1);
            gameSessionRepository.save(session);
        } else {
            // Auto-advance to next scene if at end of dialogues
            return nextScene(session);
        }
        
        return this.getGameState(session.getSessionId());
    }
    
    private GameStateResponse previousDialogue(GameSession session) {
        if (session.getCurrentDialogueIndex() > 0) {
            session.setCurrentDialogueIndex(session.getCurrentDialogueIndex() - 1);
            gameSessionRepository.save(session);
        }
        
        return this.getGameState(session.getSessionId());
    }
    
    private GameStateResponse nextScene(GameSession session) {
        // First try to find next scene in current chapter
        Optional<Background> nextBackground = backgroundRepository.findNextBackgroundInChapter(
                session.getCurrentChapter().getId(),
                session.getCurrentScene());

        if (nextBackground.isPresent()) {
            // Next scene in same chapter
            Background next = nextBackground.get();
            session.setCurrentScene(next.getSceneNumber());
            session.setCurrentBackground(next);
            session.setCurrentDialogueIndex(0);
            session.setCurrentActionIndex(0);
            gameSessionRepository.save(session);
        } else {
            // Try to move to next chapter
            Optional<Chapter> nextChapter = chapterRepository.findNextChapter(
                    session.getNovel().getId(),
                    session.getCurrentChapterNumber());

            if (nextChapter.isPresent()) {
                Chapter next = nextChapter.get();
                // Find first scene in next chapter
                List<Background> firstScenes = backgroundRepository.findByChapterIdOrderBySceneNumberAsc(next.getId());
                if (!firstScenes.isEmpty()) {
                    Background firstScene = firstScenes.get(0);
                    session.setCurrentChapter(next);
                    session.setCurrentChapterNumber(next.getChapterNumber());
                    session.setCurrentScene(firstScene.getSceneNumber());
                    session.setCurrentBackground(firstScene);
                    session.setCurrentDialogueIndex(0);
                    session.setCurrentActionIndex(0);
                    gameSessionRepository.save(session);
                }
            }
        }

        return this.getGameState(session.getSessionId());
    }
    
    private GameStateResponse previousScene(GameSession session) {
        // First try to find previous scene in current chapter
        Optional<Background> previousBackground = backgroundRepository.findPreviousBackgroundInChapter(
                session.getCurrentChapter().getId(),
                session.getCurrentScene());

        if (previousBackground.isPresent()) {
            // Previous scene in same chapter
            Background previous = previousBackground.get();
            session.setCurrentScene(previous.getSceneNumber());
            session.setCurrentBackground(previous);
            session.setCurrentDialogueIndex(0);
            session.setCurrentActionIndex(0);
            gameSessionRepository.save(session);
        } else {
            // Try to move to previous chapter
            Optional<Chapter> previousChapter = chapterRepository.findPreviousChapter(
                    session.getNovel().getId(),
                    session.getCurrentChapterNumber());

            if (previousChapter.isPresent()) {
                Chapter previous = previousChapter.get();
                // Find last scene in previous chapter
                List<Background> lastScenes = backgroundRepository.findByChapterIdOrderBySceneNumberAsc(previous.getId());
                if (!lastScenes.isEmpty()) {
                    Background lastScene = lastScenes.get(lastScenes.size() - 1);
                    session.setCurrentChapter(previous);
                    session.setCurrentChapterNumber(previous.getChapterNumber());
                    session.setCurrentScene(lastScene.getSceneNumber());
                    session.setCurrentBackground(lastScene);
                    session.setCurrentDialogueIndex(0);
                    session.setCurrentActionIndex(0);
                    gameSessionRepository.save(session);
                }
            }
        }

        return this.getGameState(session.getSessionId());
    }
    
    private GameStateResponse jumpToChapter(GameSession session, Integer chapterNumber) {
        Optional<Chapter> targetChapter = chapterRepository
                .findByNovelIdAndChapterNumber(session.getNovel().getId(), chapterNumber);

        if (targetChapter.isPresent()) {
            Chapter chapter = targetChapter.get();
            List<Background> firstScenes = backgroundRepository.findByChapterIdOrderBySceneNumberAsc(chapter.getId());

            if (!firstScenes.isEmpty()) {
                Background firstScene = firstScenes.get(0);
                session.setCurrentChapter(chapter);
                session.setCurrentChapterNumber(chapter.getChapterNumber());
                session.setCurrentScene(firstScene.getSceneNumber());
                session.setCurrentBackground(firstScene);
                session.setCurrentDialogueIndex(0);
                session.setCurrentActionIndex(0);
                gameSessionRepository.save(session);
            }
        }

        return this.getGameState(session.getSessionId());
    }
    
    private GameStateResponse jumpToScene(GameSession session, Integer sceneNumber) {
        Optional<Background> targetBackground = backgroundRepository
                .findByChapterIdAndSceneNumber(session.getCurrentChapter().getId(), sceneNumber);

        if (targetBackground.isPresent()) {
            Background background = targetBackground.get();
            session.setCurrentScene(background.getSceneNumber());
            session.setCurrentBackground(background);
            session.setCurrentDialogueIndex(0);
            session.setCurrentActionIndex(0);
            gameSessionRepository.save(session);
        }

        return this.getGameState(session.getSessionId());
    }
    
    private GameStateResponse saveGame(GameSession session) {
        // Implementation for saving game state
        session.setLastSaveData(buildSaveData(session));
        gameSessionRepository.save(session);
        return this.getGameState(session.getSessionId());
    }
    
    private GameStateResponse pauseGame(GameSession session) {
        session.setSessionState(GameSession.SessionState.PAUSED);
        gameSessionRepository.save(session);
        return this.getGameState(session.getSessionId());
    }
    
    private GameStateResponse resumeGame(GameSession session) {
        session.setSessionState(GameSession.SessionState.ACTIVE);
        gameSessionRepository.save(session);
        return this.getGameState(session.getSessionId());
    }
    
    private boolean hasNextScene(Long novelId, Integer currentChapter, Integer currentScene) {
        try {
            return backgroundRepository.findNextBackground(novelId, currentChapter, currentScene).isPresent();
        } catch (Exception e) {
            log.error("Error checking next scene for novel {}, chapter {}, scene {}: {}",
                     novelId, currentChapter, currentScene, e.getMessage());
            return false;
        }
    }

    private boolean hasPreviousScene(Long novelId, Integer currentChapter, Integer currentScene) {
        try {
            return backgroundRepository.findPreviousBackground(novelId, currentChapter, currentScene).isPresent();
        } catch (Exception e) {
            log.error("Error checking previous scene for novel {}, chapter {}, scene {}: {}",
                     novelId, currentChapter, currentScene, e.getMessage());
            return false;
        }
    }
    
    private GameStateResponse.GameProgressDto buildProgressInfo(GameSession session) {
        Long totalScenesInChapter = backgroundRepository.countByChapterId(session.getCurrentChapter().getId());

        Long totalDialoguesInScene = dialogueRepository.countByBackgroundId(
                session.getCurrentBackground().getId());

        Long totalActionsInScene = gameActionRepository.countByBackgroundId(
                session.getCurrentBackground().getId());

        double chapterProgress = totalScenesInChapter > 0 ?
                (double) session.getCurrentScene() / totalScenesInChapter * 100 : 0;

        double overallProgress = session.getNovel().getTotalChapters() > 0 ?
                (double) session.getCurrentChapterNumber() / session.getNovel().getTotalChapters() * 100 : 0;
        
        return GameStateResponse.GameProgressDto.builder()
                .totalChapters(session.getNovel().getTotalChapters())
                .totalScenesInChapter(totalScenesInChapter.intValue())
                .totalDialoguesInScene(totalDialoguesInScene.intValue())
                .totalActionsInScene(totalActionsInScene.intValue())
                .chapterProgress(chapterProgress)
                .overallProgress(overallProgress)
                .build();
    }
    
    private String buildSaveData(GameSession session) {
        // Simple JSON-like save data
        return String.format("{\"chapter\":%d,\"scene\":%d,\"dialogue\":%d,\"action\":%d}",
                session.getCurrentChapterNumber(),
                session.getCurrentScene(),
                session.getCurrentDialogueIndex(),
                session.getCurrentActionIndex());
    }
    
    // Mapping methods
    private GameStateResponse.BackgroundDto mapToBackgroundDto(Background background) {
        return GameStateResponse.BackgroundDto.builder()
                .id(background.getId())
                .name(background.getName())
                .description(background.getDescription())
                .chapterNumber(background.getChapter().getChapterNumber())
                .sceneNumber(background.getSceneNumber())
                .backgroundImageUrl(background.getBackgroundImageUrl())
                .backgroundMusicUrl(background.getBackgroundMusicUrl())
                .ambientSoundUrl(background.getAmbientSoundUrl())
                .weatherEffect(background.getWeatherEffect())
                .timeOfDay(background.getTimeOfDay())
                .build();
    }
    
    private GameStateResponse.DialogueDto mapToDialogueDto(Dialogue dialogue) {
        return GameStateResponse.DialogueDto.builder()
                .id(dialogue.getId())
                .text(dialogue.getText())
                .sequenceOrder(dialogue.getSequenceOrder())
                .dialogueType(dialogue.getDialogueType())
                .emotion(dialogue.getEmotion())
                .voiceFileUrl(dialogue.getVoiceFileUrl())
                .displayDurationMs(dialogue.getDisplayDurationMs())
                .autoAdvance(dialogue.getAutoAdvance())
                .characterPosition(dialogue.getCharacterPosition())
                .character(mapToCharacterDto(dialogue.getCharacter()))
                .build();
    }
    
    private GameStateResponse.CharacterDto mapToCharacterDto(Character character) {
        return GameStateResponse.CharacterDto.builder()
                .id(character.getId())
                .name(character.getName())
                .description(character.getDescription())
                .avatarUrl(character.getAvatarUrl())
                .characterType(character.getCharacterType().toString())
                .voiceUrl(character.getVoiceUrl())
                .build();
    }
    
    private GameStateResponse.GameActionDto mapToGameActionDto(GameAction gameAction) {
        return GameStateResponse.GameActionDto.builder()
                .id(gameAction.getId())
                .actionType(gameAction.getActionType())
                .sequenceOrder(gameAction.getSequenceOrder())
                .targetElement(gameAction.getTargetElement())
                .actionValue(gameAction.getActionValue())
                .durationMs(gameAction.getDurationMs())
                .delayBeforeMs(gameAction.getDelayBeforeMs())
                .triggerCondition(gameAction.getTriggerCondition())
                .isBlocking(gameAction.getIsBlocking())
                .description(gameAction.getDescription())
                .build();
    }
}
